# -*- coding: utf-8 -*-
"""
MQTT物联网网关服务 (最终稳定版 v2.6 - 统一指令出口)
=========================================================
- 架构重构: 新增Redis指令监听线程，统一处理来自Web端的指令请求 (v2.6)
"""
import paho.mqtt.client as mqtt
import json
import mysql.connector
from mysql.connector import Error
import time
import random
import redis
from datetime import datetime
from decimal import Decimal
import threading

# --- 配置信息 ---
MQTT_BROKER_IP = 'localhost'
MQTT_BROKER_PORT = 1883
MQTT_TIMEOUT = 60
DATA_TOPIC = "stm32/data"
COMMAND_TOPIC_FORMAT = "stm32/command/{client_id}"
ACK_TOPIC = "stm32/ack/+"

COMMAND_TIMEOUT = 3 

MYSQL_CONFIG = {
    'host': 'localhost',
    'user': 'm2joy',
    'password': 'Liu041121@',
    'database': 'iot_data'
}

REDIS_HOST = 'localhost'
REDIS_PORT = 6379
REDIS_DATA_CHANNEL = 'iot_data_stream'
REDIS_COMMAND_CHANNEL = 'iot_command_requests' 

SMOOTHING_CHECKS_ENABLED = True
MAX_TEMP_CHANGE_PER_READING = 10.0
MAX_HUMIDITY_CHANGE_PER_READING = 25.0


class MqttGateway:
    def __init__(self, broker_ip, port, timeout):
        self.broker_ip = broker_ip
        self.broker_port = port
        self.timeout = timeout
        self.client = None
        self.last_valid_readings = {}

        self.pending_acks = {} 
        self.pending_acks_lock = threading.Lock()

        try:
            self.redis_client = redis.Redis(host=REDIS_HOST, port=REDIS_PORT, decode_responses=True)
            self.redis_client.ping()
            print("✅ 成功连接到Redis服务器")
        except redis.exceptions.ConnectionError as e:
            print(f"❌ 连接Redis失败: {e}")
            self.redis_client = None

        self.start_client()
        
        # 启动后台超时监控线程
        watchdog_thread = threading.Thread(target=self._ack_watchdog, daemon=True)
        watchdog_thread.start()
        print("👁️  指令超时监视器已启动")

        # 启动后台Redis指令监听线程
        cmd_listener_thread = threading.Thread(target=self._redis_command_listener, daemon=True)
        cmd_listener_thread.start()
        print("👂 Redis指令监听器已启动")


    def _ack_watchdog(self):
        """一个在后台运行的守护线程，用于检查超时的指令。"""
        while True:
            time.sleep(2)
            now = time.time()
            timed_out_commands = []
            
            with self.pending_acks_lock:
                for (client_id, command), timestamp in self.pending_acks.items():
                    if now - timestamp > COMMAND_TIMEOUT:
                        timed_out_commands.append((client_id, command))
            
            for client_id, command in timed_out_commands:
                print(f"❌ 指令超时: 设备 {client_id} 未能在 {COMMAND_TIMEOUT} 秒内确认执行 '{command}'。")
                
                with self.pending_acks_lock:
                    self.pending_acks.pop((client_id, command), None)
                
                failure_message = {
                    'type': 'command_failure',
                    'client_id': client_id,
                    'command': command,
                    'message': f"指令失败: 设备 {client_id} 未在 {COMMAND_TIMEOUT} 秒内响应 '{command}' 操作。"
                }
                self.publish_to_redis(failure_message)

    def _redis_command_listener(self):
        """一个后台线程，监听来自Web端的指令请求。"""
        r = redis.Redis(host=REDIS_HOST, port=REDIS_PORT)
        pubsub = r.pubsub()
        pubsub.subscribe(REDIS_COMMAND_CHANNEL)
        
        for message in pubsub.listen():
            if message['type'] == 'message':
                try:
                    data = json.loads(message['data'])
                    client_id = data.get('client_id')
                    command = data.get('command')
                    if client_id and command:
                        print(f"📩 收到来自Web端的Redis指令请求: {data}")
                        # 调用自己的publish_command来统一处理
                        self.publish_command(client_id, command)
                except json.JSONDecodeError:
                    print(f"❌ 无法解析收到的Redis指令: {message['data']}")
                except Exception as e:
                    print(f"❌ 处理Redis指令时出错: {e}")


    def get_db_connection(self):
        try:
            return mysql.connector.connect(**MYSQL_CONFIG)
        except Error as e:
            print(f"❌ 数据库连接失败: {e}")
            return None

    def start_client(self):
        client_name = f"mqtt_gateway_{random.randint(1000, 9999)}"
        self.client = mqtt.Client(mqtt.CallbackAPIVersion.VERSION2, client_name)
        self.client.on_connect = self.on_connect
        self.client.on_message = self.on_message
        try:
            self.client.connect(self.broker_ip, self.broker_port, self.timeout)
            self.client.loop_start()
        except Exception as e:
            print(f"❌ 连接MQTT代理服务器失败: {e}")

    def on_connect(self, client, userdata, flags, rc, properties=None):
        if rc == 0:
            print("✅ 成功连接到MQTT代理服务器!")
            client.subscribe(DATA_TOPIC)
            print(f"📡 已订阅主题: {DATA_TOPIC}")
            client.subscribe(ACK_TOPIC)
            print(f"📡 已订阅主题: {ACK_TOPIC}")
        else:
            print(f"❌ 连接失败，错误代码: {rc}")

    def get_latest_device_status(self, client_id):
        conn = self.get_db_connection()
        if not conn: return None
        try:
            with conn.cursor(dictionary=True) as cursor:
                query = "SELECT fan_status, light_status, control_mode, temperature, humidity, light_intensity FROM sensor_readings WHERE client_id = %s ORDER BY timestamp DESC LIMIT 1"
                cursor.execute(query, (client_id,))
                return cursor.fetchone()
        finally:
            if conn.is_connected(): conn.close()
            
    def publish_to_redis(self, record_dict):
        if not self.redis_client: return
        try:
            for key, value in record_dict.items():
                if isinstance(value, datetime):
                    record_dict[key] = value.isoformat()
                elif isinstance(value, Decimal):
                    record_dict[key] = float(value)
            
            message_to_publish = json.dumps(record_dict)
            # 发布到数据频道，供前端SSE消费
            self.redis_client.publish(REDIS_DATA_CHANNEL, message_to_publish)
            
            if record_dict.get('type') == 'command_failure':
                 print(f"📡 已将指令失败通知发布到Redis频道 '{REDIS_DATA_CHANNEL}'")
            else:
                 print(f"📡 已将新记录 (ID: {record_dict.get('id')}) 发布到Redis频道 '{REDIS_DATA_CHANNEL}'")
        except Exception as e:
            print(f"❌ 发布到Redis失败: {e}")

    def update_device_status_on_ack(self, client_id, command):
        new_fan_status, new_light_status = None, None
        if command == "open_fan": new_fan_status = 1
        elif command == "close_fan": new_fan_status = 0
        elif command == "open_light": new_light_status = 1
        elif command == "close_light": new_light_status = 0
        else: return None

        conn = self.get_db_connection()
        if not conn: return None

        try:
            with conn.cursor(dictionary=True) as cursor:
                query = "SELECT temperature, humidity, light_intensity, fan_status, light_status, control_mode FROM sensor_readings WHERE client_id = %s ORDER BY timestamp DESC LIMIT 1"
                cursor.execute(query, (client_id,))
                latest = cursor.fetchone()
                if not latest: return None

                current_mode = latest['control_mode']
                fan_status = new_fan_status if new_fan_status is not None else latest['fan_status']
                light_status = new_light_status if new_light_status is not None else latest['light_status']
                
                sql = """INSERT INTO sensor_readings (client_id, temperature, humidity, light_intensity, fan_status, light_status, control_mode) 
                         VALUES (%s, %s, %s, %s, %s, %s, %s)"""
                val = (client_id, latest['temperature'], latest['humidity'], latest['light_intensity'], fan_status, light_status, current_mode)
                cursor.execute(sql, val)
                new_id = cursor.lastrowid
                conn.commit()
                print(f"💾 指令ACK确认: 已将 {client_id} 的新状态保存到数据库, ID={new_id}, 模式为'{current_mode}'")
                
                cursor.execute("SELECT * FROM sensor_readings WHERE id = %s", (new_id,))
                return cursor.fetchone()
        except Error as e:
            print(f"❌ ACK后更新数据库失败: {e}")
            return None
        finally:
            if conn.is_connected(): conn.close()

    def handle_ack_message(self, msg):
        try:
            client_id = msg.topic.split('/')[-1]
            payload_str = msg.payload.decode('utf-8')
            core_str = payload_str.strip('{}').strip('"')
            
            if core_str and core_str.endswith('_ok'):
                command = core_str[:-3]
                print(f"✅ 收到设备 {client_id} 对指令 '{command}' 的成功确认(ACK)。")

                with self.pending_acks_lock:
                    self.pending_acks.pop((client_id, command), None)
                
                new_record = self.update_device_status_on_ack(client_id, command)
                if new_record:
                    new_record['ack_for_command'] = command
                    self.publish_to_redis(new_record)
        except Exception as e:
            print(f"❌ 处理ACK消息时出错: {e}")

    def on_message(self, client, userdata, msg):
        if msg.topic.startswith('stm32/ack/'):
            self.handle_ack_message(msg)
            return
        if msg.topic != DATA_TOPIC:
            return

        try:
            payload_str = msg.payload.decode('utf-8')
            print(f"📨 收到消息: {payload_str}")

            content = payload_str.strip('{}')
            parts = content.split(';')
            if len(parts) != 4: return

            client_id, temp_str, hum_str, light_str = parts
            temperature, humidity, light_intensity = float(temp_str), float(hum_str), float(light_str)

            if SMOOTHING_CHECKS_ENABLED:
                last_reading = self.last_valid_readings.get(client_id)
                if last_reading:
                    temp_diff = abs(temperature - last_reading['temperature'])
                    if temp_diff > MAX_TEMP_CHANGE_PER_READING:
                        print(f"⚠️  数据异常: 设备 {client_id} 温度突变 {temp_diff:.1f}°C，数据已丢弃。")
                        return

                    hum_diff = abs(humidity - last_reading['humidity'])
                    if hum_diff > MAX_HUMIDITY_CHANGE_PER_READING:
                        print(f"⚠️  数据异常: 设备 {client_id} 湿度突变 {hum_diff:.1f}%，数据已丢弃。")
                        return
                
                self.last_valid_readings[client_id] = {'temperature': temperature, 'humidity': humidity}

            latest_status = self.get_latest_device_status(client_id)
            db_fan_status = bool(latest_status['fan_status']) if latest_status else False
            db_light_status = bool(latest_status['light_status']) if latest_status else False
            db_control_mode = latest_status['control_mode'] if latest_status else 'auto'

            if db_control_mode == 'auto':
                if temperature >= 30.0 and not db_fan_status:
                    self.publish_command(client_id, "open_fan")
                elif temperature <= 25.0 and db_fan_status:
                    self.publish_command(client_id, "close_fan")
                
                if light_intensity < 50.0 and not db_light_status:
                    self.publish_command(client_id, "open_light")
                elif light_intensity >= 50.0 and db_light_status:
                    self.publish_command(client_id, "close_light")
            
            new_fan_state, new_light_state = db_fan_status, db_light_status
            new_mode = db_control_mode

            conn = self.get_db_connection()
            if not conn: return
            try:
                with conn.cursor(dictionary=True) as cursor:
                    sql = """INSERT INTO sensor_readings (client_id, temperature, humidity, light_intensity, fan_status, light_status, control_mode) 
                             VALUES (%s, %s, %s, %s, %s, %s, %s)"""
                    val = (client_id, temperature, humidity, light_intensity, 1 if new_fan_state else 0, 1 if new_light_state else 0, new_mode)
                    cursor.execute(sql, val)
                    new_id = cursor.lastrowid
                    conn.commit()
                    print(f"💾 数据已保存到数据库, ID={new_id}")
                    
                    cursor.execute("SELECT * FROM sensor_readings WHERE id = %s", (new_id,))
                    new_record = cursor.fetchone()
                    if new_record:
                        self.publish_to_redis(new_record)
            finally:
                if conn.is_connected(): conn.close()

        except Exception as e:
            print(f"❌ 处理数据消息时发生未知错误: {e}")

    def publish_command(self, client_id, command):
        command_topic = COMMAND_TOPIC_FORMAT.format(client_id=client_id)
        command_payload = json.dumps({"command": command, "timestamp": time.time()})
        self.client.publish(command_topic, command_payload, qos=1)
        
        with self.pending_acks_lock:
            self.pending_acks[(client_id, command)] = time.time()
        
        print(f"📤 已发送指令 '{command}' 到 '{command_topic}' 并等待ACK...")

if __name__ == '__main__':
    gateway = MqttGateway(MQTT_BROKER_IP, MQTT_BROKER_PORT, MQTT_TIMEOUT)
    print("🚀 MQTT物联网网关服务已启动 (v2.6 - 统一指令出口版)")
    try:
        while True: time.sleep(1)
    except KeyboardInterrupt:
        if gateway.client: gateway.client.loop_stop()
        print("\n✅ 网关服务已安全关闭")